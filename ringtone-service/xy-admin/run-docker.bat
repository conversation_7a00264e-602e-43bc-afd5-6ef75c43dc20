@echo off
chcp 65001 >nul

REM ================================
REM XY-Admin Docker 运行脚本 (Windows)
REM ================================
REM 解决Spring Boot配置文件找不到的问题
REM 使用方法: run-docker.bat [PROFILE] [MARK]
REM 示例: run-docker.bat dev-test JB

REM 设置默认参数
set PROFILE=%1
set MARK=%2
if "%PROFILE%"=="" set PROFILE=dev-test
if "%MARK%"=="" set MARK=JB

set CONTAINER_NAME=xy-admin
set IMAGE_NAME=xy-admin
set HOST_PORT=8915
set DEBUG_PORT=5006

echo ================================
echo XY-Admin Docker 部署脚本
echo ================================
echo 环境配置: %PROFILE%
echo 标记: %MARK%
echo 容器名称: %CONTAINER_NAME%
echo ================================

REM 停止并删除现有容器
echo 正在停止现有容器...
docker stop %CONTAINER_NAME% 2>nul
docker rm %CONTAINER_NAME% 2>nul

REM 构建Docker镜像
echo 正在构建Docker镜像...
docker build -f Dockerfile --build-arg PROFILE=%PROFILE% --build-arg MARK=%MARK% -t %IMAGE_NAME% .

if %errorlevel% neq 0 (
    echo ❌ Docker镜像构建失败！
    pause
    exit /b 1
)

echo ✅ Docker镜像构建成功！

REM 创建必要的目录（Windows路径）
echo 正在创建数据目录...
if not exist "C:\data\xy-admin\lib" mkdir "C:\data\xy-admin\lib"
if not exist "C:\data\xy-admin\logs" mkdir "C:\data\xy-admin\logs"
if not exist "C:\data\xy-admin\config" mkdir "C:\data\xy-admin\config"

REM 运行Docker容器
echo 正在启动Docker容器...
docker run -d ^
    --name %CONTAINER_NAME% ^
    --hostname xy-admin ^
    -p %HOST_PORT%:8915 ^
    -p %DEBUG_PORT%:5006 ^
    -v C:\data\xy-admin\lib:/app/lib ^
    -v C:\data\xy-admin\logs:/app/logs ^
    -v C:\data\xy-admin\config:/app/config ^
    -e SPRING_PROFILE=%PROFILE% ^
    -e MARK_NAME=%MARK% ^
    -e TZ=Asia/Shanghai ^
    -e JAVA_OPTS="-server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC" ^
    --network 1panel-network ^
    --restart unless-stopped ^
    %IMAGE_NAME%

if %errorlevel% equ 0 (
    echo ✅ 容器启动成功！
    echo ================================
    echo 服务信息:
    echo - 服务端口: http://localhost:%HOST_PORT%/admin
    echo - 调试端口: %DEBUG_PORT%
    echo - 容器名称: %CONTAINER_NAME%
    echo - 环境配置: %PROFILE%
    echo ================================
    echo 查看日志: docker logs -f %CONTAINER_NAME%
    echo 进入容器: docker exec -it %CONTAINER_NAME% sh
    echo 停止容器: docker stop %CONTAINER_NAME%
    echo ================================
) else (
    echo ❌ 容器启动失败！
    pause
    exit /b 1
)

pause
