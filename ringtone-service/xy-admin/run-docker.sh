#!/bin/bash

# ================================
# XY-Admin Docker 运行脚本
# ================================
# 解决Spring Boot配置文件找不到的问题
# 使用方法: ./run-docker.sh [PROFILE] [MARK]
# 示例: ./run-docker.sh dev-test JB

# 设置默认参数
PROFILE=${1:-dev-test}
MARK=${2:-JB}
CONTAINER_NAME="xy-admin"
IMAGE_NAME="xy-admin"
HOST_PORT=8915
DEBUG_PORT=5006

echo "================================"
echo "XY-Admin Docker 部署脚本"
echo "================================"
echo "环境配置: ${PROFILE}"
echo "标记: ${MARK}"
echo "容器名称: ${CONTAINER_NAME}"
echo "================================"

# 停止并删除现有容器
echo "正在停止现有容器..."
docker stop ${CONTAINER_NAME} 2>/dev/null || true
docker rm ${CONTAINER_NAME} 2>/dev/null || true

# 构建Docker镜像
echo "正在构建Docker镜像..."
docker build -f Dockerfile \
    --build-arg PROFILE=${PROFILE} \
    --build-arg MARK=${MARK} \
    -t ${IMAGE_NAME} .

if [ $? -ne 0 ]; then
    echo "❌ Docker镜像构建失败！"
    exit 1
fi

echo "✅ Docker镜像构建成功！"

# 创建必要的目录
echo "正在创建数据目录..."
mkdir -p /data/xy-admin/lib
mkdir -p /data/xy-admin/logs
mkdir -p /data/xy-admin/config

# 运行Docker容器
echo "正在启动Docker容器..."
docker run -d \
    --name ${CONTAINER_NAME} \
    --hostname xy-admin \
    -p ${HOST_PORT}:8915 \
    -p ${DEBUG_PORT}:5006 \
    -v /data/xy-admin/lib:/app/lib \
    -v /data/xy-admin/logs:/app/logs \
    -v /data/xy-admin/config:/app/config \
    -e SPRING_PROFILE=${PROFILE} \
    -e MARK_NAME=${MARK} \
    -e TZ=Asia/Shanghai \
    -e JAVA_OPTS="-server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC" \
    --network 1panel-network \
    --restart unless-stopped \
    ${IMAGE_NAME}

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功！"
    echo "================================"
    echo "服务信息:"
    echo "- 服务端口: http://localhost:${HOST_PORT}/admin"
    echo "- 调试端口: ${DEBUG_PORT}"
    echo "- 容器名称: ${CONTAINER_NAME}"
    echo "- 环境配置: ${PROFILE}"
    echo "================================"
    echo "查看日志: docker logs -f ${CONTAINER_NAME}"
    echo "进入容器: docker exec -it ${CONTAINER_NAME} sh"
    echo "停止容器: docker stop ${CONTAINER_NAME}"
    echo "================================"
else
    echo "❌ 容器启动失败！"
    exit 1
fi
